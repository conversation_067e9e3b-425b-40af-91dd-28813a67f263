FROM php:7.4-apache

# Installation des dépendances système nécessaires
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libzip-dev \
    libicu-dev \
    libxml2-dev \
    curl \
    unzip \
    libmemcached-dev \
    libzstd-dev \
    apache2-utils \
    ca-certificates \
    && update-ca-certificates \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd zip pdo pdo_mysql mysqli intl opcache \
    && pecl install memcached || true \
    && echo "extension=memcached.so" > /usr/local/etc/php/conf.d/memcached.ini \
    && curl -sL https://deb.nodesource.com/setup_14.x | bash - \
    && apt-get install -y nodejs \
    && a2enmod rewrite headers || true

# Installer Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN docker-php-ext-install soap




# Configuration PHP personnalisée
COPY php.ini /usr/local/etc/php/conf.d/

# Copie des fichiers de l'application
COPY . /var/www/html/

# Attribution des bons droits
RUN chown -R www-data:www-data /var/www/html

# Validation : afficher version composer pour debug build
RUN composer --version

EXPOSE 80
