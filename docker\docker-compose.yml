version: '3.7'

services:
  php:
    image: php:7.4-apache
    container_name: php7.4_apache
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /home/<USER>/projects/Ze-Camping:/home/<USER>
      - /home/<USER>/projects/ze-camping-v5:/home/<USER>
      - /home/<USER>/projects/data2:/home/<USER>
      - /home/<USER>/projects/requestip:/home/<USER>
      - ./ressources/000-default.conf:/etc/apache2/sites-enabled/000-default.conf
      - ./ressources/custom-apache-config.conf:/etc/apache2/conf-enabled/custom-apache-config.conf
      - ./config/php.ini:/usr/local/etc/php/conf.d/custom-php.ini
      - ./init.sh:/init.sh
      - ./certs:/etc/ssl/certs  
    networks:
      - app_network
    extra_hosts:
      - "www2.ze.fr:127.0.0.1"
      - "bo2.ze.fr:127.0.0.1"
    depends_on:
      - mariadb
      - memcached
    command: ["bash", "/init.sh"]

  mariadb:
    image: mariadb:10.5
    container_name: mariadb_10_5
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: locasunv4
    volumes:
      - mariadb_data:/var/lib/mysql
    networks:
      - app_network
    ports:
      - "3306:3306"  # Expose le port 3306 pour accéder à MariaDB depuis l'extérieur
    command: --innodb_default_row_format=DYNAMIC --innodb_strict_mode=0 --lower_case_table_names=1

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    environment:
      PMA_HOST: mariadb_10_5
      MYSQL_ROOT_PASSWORD: rootpass
    ports:
      - "8080:80"
    networks:
      - app_network

  memcached:
    image: memcached:latest
    container_name: memcached
    networks:
      - app_network
    ports:
      - "44211:44211"
    command: ["memcached", "-m", "64", "-p", "44211", "-u", "memcached", "-c", "1024"]

networks:
  app_network:
    driver: bridge

volumes:
  mariadb_data:
