docker-compose up
docker-compose up -d
//docker-compose up --build
//rebuild 
docker-compose down
docker-compose build --no-cache
docker-compose up -d
//se connecter sur le serv
winpty docker exec -it php7.4_apache bash
winpty docker exec -it mariadb_10_5 bash


1//Modifier le fichier hosts sur ton système Windows

Ouvre Notepad en mode administrateur (Clique droit sur Notepad et "Exécuter en tant qu'administrateur").

Va à C:\Windows\System32\drivers\etc\hosts.

Ajoute les lignes suivantes dans ce fichier :

127.0.0.1    www2.ze.fr
127.0.0.1    bo2.ze.fr

//importer bdd
aalil@LAPTOP-SG0ECR7J MINGW64 /d/projet/docker
$ sed -i 's/ENGINE=InnoDB/ENGINE=InnoDB ROW_FORMAT=DYNAMIC/g' dump/locasunv4_20250416.sql

$ docker exec -i mariadb_10_5 mysql -u root -prootpass locasunv4 < dump/locasunv4_20250416.sql


2// Accéder à la base de données via le terminal Docker
winpty docker exec -it mariadb_10_5 mysql -u root -p

3// Exporter la base de données SQL
winpty docker exec mariadb_10_5 mysqldump -u root -p locasunv4 > locasunv4_20250416.sql


4//Étapes pour configurer les limites des ressources pour WSL 2 en cas de problème au lancement
Créer ou éditer le fichier .wslconfig

Le fichier .wslconfig est généralement situé dans le répertoire de votre utilisateur (C:\Users\<USER>\).

Si le fichier n'existe pas déjà, vous pouvez le créer manuellement.

Éditer le fichier .wslconfig

Ouvrez le fichier .wslconfig avec un éditeur de texte (par exemple, Notepad).

Ajoutez les paramètres suivants pour configurer les ressources allouées à WSL 2 :


[wsl2]
memory=8GB         # Limite de la mémoire (ajustez selon vos besoins)
processors=4       # Nombre de cœurs CPU alloués
swap=4GB           # Taille du swap (optionnel, vous pouvez supprimer si vous ne voulez pas de swap)
Vous pouvez ajuster les valeurs en fonction de vos besoins :



Redémarrer WSL 2

wsl --shutdown
Cette commande arrête toutes les instances WSL 2 en cours. Vous pouvez ensuite les redémarrer en utilisant wsl ou en lançant Docker Desktop.

Vérifier la configuration
Pour vérifier les paramètres de configuration, vous pouvez exécuter cette commande dans PowerShell :
wsl --list --verbose


//5 connexion à la bdd http://localhost:8080/


*********************
docker images
Tu verras quelque chose comme :

nginx
Copier
Modifier
REPOSITORY        TAG       IMAGE ID       CREATED              SIZE
mon-php-app       latest    abc123456789   5 minutes ago        480MB
 Et après ?
Cette image sert de modèle pour lancer des conteneurs.
Par exemple, tu peux la lancer avec :

docker run -it --rm -p 8080:80 mon-php-app
Cela lance ton application dans un conteneur accessible via http://localhost:8080.
************************

Copie tes projets depuis Windows vers WSL

mkdir -p ~/projects/Ze-Camping
mkdir -p ~/projects/ze-camping-v5
mkdir -p ~/projects/docker

1-remplace d pas c si tonn projet est sous la c, adapte le chemin 
cp -r /mnt/d/projet/Ze-Camping/* ~/projects/Ze-Camping/
cp -r /mnt/d/projet/ze-camping-v5/* ~/projects/ze-camping-v5/
cp -r /mnt/d/projet/docker/* ~/projects/docker/
2-Modifie ton docker-compose.yml
volumes:
      - /home/<USER>/projects/Ze-Camping:/home/<USER>
      - /home/<USER>/projects/ze-camping-v5:/home/<USER>
	  
3- Remplace tonuser par ton vrai nom d’utilisateur sous WSL :
 lance "whoami" pour trouver le nom d'utilisateur
 
4- Lance ton conteneur depuis WSL
cd ~/projects/docker
docker-compose up -d

6- Ouvre le projet dans PHPStorm via WSL

Lance PHPStorm.

Clique sur "Open".

Dans la fenêtre de sélection, tape :

\\wsl$\Ubuntu\home\ali\projects\Ze-Camping

(Remplace Ubuntu si tu as une autre distro WSL)

 wsl --list --verbose
  NAME              STATE           VERSION
* Debian            Running         2
  docker-desktop    Running         2

\\wsl$\Debian\home\ali\projects\Ze-Camping

Tu peux ouvrir et éditer directement dans ce chemin sans passer par D:\ — c’est ça la clé pour éviter les ralentissements.
 Résultat : Plus de latence liée à D:


*******************************

-d memory_limit=512M