<VirtualHost *:80>
    ServerName bo2.ze.fr
    ServerAlias www2.ze.fr
    DocumentRoot /home/<USER>/public

   <Directory /home/<USER>/public>
        AllowOverride All
        Require all granted
        Options FollowSymLinks
    </Directory>

    # Compression GZIP
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript
    </IfModule>

    # Cache des fichiers statiques
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </IfModule>

    # Cache-Control Header
    <IfModule mod_headers.c>
        Header set Cache-Control "public, max-age=31536000"
    </IfModule>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName bo2.ze.fr
    ServerAlias www2.ze.fr
    DocumentRoot /home/<USER>/public

    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/www2.ze.fr+1.pem
    SSLCertificateKeyFile /etc/ssl/certs/www2.ze.fr+1-key.pem

    <Directory /home/<USER>/public>
        AllowOverride All
        Require all granted
        Options FollowSymLinks
    </Directory>

    # Compression GZIP
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript
    </IfModule>

    # Cache des fichiers statiques
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </IfModule>

    # Cache-Control Header
    <IfModule mod_headers.c>
        Header set Cache-Control "public, max-age=31536000"
    </IfModule>

    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined

</VirtualHost>

<IfModule mpm_prefork_module>
    StartServers             2
    MinSpareServers          2
    MaxSpareServers          5
    MaxRequestWorkers       10
    MaxConnectionsPerChild 3000
</IfModule>
